import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint
import logging
from model.loss import compute_dynamic_degree

logger = logging.getLogger(__name__)

class MemoryEfficientDecoder(nn.Module):
    """
    Memory-efficient decoder for temporal inpainting with probability learning
    Uses progressive upsampling and skip connections
    """
    
    def __init__(self, 
                 in_channels,
                 out_channels=1,  # 单通道输出概率值
                 img_size=256,
                 patch_size=8,
                 num_classes=1,  # 修改为1，输出概率值
                 use_checkpoint=True):
        super().__init__()
        self.img_size = img_size
        self.patch_size = patch_size
        self.use_checkpoint = use_checkpoint
        
        # Progressive upsampling with skip connections
        self.upsample_blocks = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_channels, in_channels // 2, 3, padding=1),
                nn.GroupNorm(16, in_channels // 2),
                nn.GELU(),
                nn.ConvTranspose2d(in_channels // 2, in_channels // 2, 4, 2, 1),
                nn.GroupNorm(16, in_channels // 2),
                nn.GELU()
            ),
            nn.Sequential(
                nn.Conv2d(in_channels // 2, in_channels // 4, 3, padding=1),
                nn.GroupNorm(8, in_channels // 4),
                nn.GELU(),
                nn.ConvTranspose2d(in_channels // 4, in_channels // 4, 4, 2, 1),
                nn.GroupNorm(8, in_channels // 4),
                nn.GELU()
            ),
            nn.Sequential(
                nn.Conv2d(in_channels // 4, in_channels // 8, 3, padding=1),
                nn.GroupNorm(4, in_channels // 8),
                nn.GELU(),
                nn.ConvTranspose2d(in_channels // 8, in_channels // 8, 4, 2, 1),
                nn.GroupNorm(4, in_channels // 8),
                nn.GELU()
            )
        ])
        
        # Skip connection gates for dynamic region attention
        self.skip_gates = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(in_channels // (2 ** (i+1)), in_channels // (2 ** (i+1)), 3, padding=1),
                nn.GroupNorm(8 // (2 ** i), in_channels // (2 ** (i+1))),
                nn.GELU(),
                nn.Conv2d(in_channels // (2 ** (i+1)), 1, 1),
                nn.Sigmoid()
            )
            for i in range(3)
        ])
        
        # Final prediction layers - 输出单通道概率值
        self.final_conv = nn.Sequential(
            nn.Conv2d(in_channels // 8, 32, 3, padding=1),
            nn.GroupNorm(4, 32),
            nn.GELU(),
            nn.Conv2d(32, num_classes, 1)  # 单通道输出
        )
        
        # Confidence estimation (raw logits; Sigmoid removed – will be applied in loss when needed)
        self.confidence = nn.Sequential(
            nn.Conv2d(in_channels // 8, 32, 3, padding=1),
            nn.GroupNorm(4, 32),
            nn.GELU(),
            nn.Conv2d(32, 1, 1)
        )
    
    def _forward_impl(self, x, features=None, target_t_ratio=0.5, water_frequency=None):
        """Implementation of forward pass with dynamic skip connections"""
        # 处理None类型的target_t_ratio
        if target_t_ratio is None:
            target_t_ratio = 0.5
            
        # Process upsampling blocks
        for i, upsample in enumerate(self.upsample_blocks):
            x = upsample(x)
            
            # Add skip connections if available
            if features is not None and i < len(features) and i < len(self.skip_gates):
                skip = features[-(i+1)]
                B_s, L_s, C_s = skip.shape
                
                # 计算patch网格大小
                H_p = W_p = self.img_size // self.patch_size
                num_patches = H_p * W_p
                
                # 快速路径检查：如果明显不符合维度要求，直接跳过
                if num_patches <= 0 or L_s < num_patches:
                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug(f"Skip connection at stage {i}: incompatible dimensions (L_s={L_s}, num_patches={num_patches})")
                    continue
                
                # 规范化skip feature的长度，确保能被整除
                T_s = max(1, L_s // num_patches)
                L_s_exact = T_s * num_patches
                
                # 如果长度不匹配，裁剪为整数倍
                if L_s != L_s_exact:
                    skip = skip[:, :L_s_exact, :]
                    
                # 计算目标时间点索引
                t_ratio = 0.5 if target_t_ratio is None else target_t_ratio
                t_idx = min(int(torch.round(torch.tensor(t_ratio * (T_s - 1))).item()), T_s - 1)
                t_idx = max(0, t_idx)  # 确保索引有效
                
                # 重塑为时空序列并提取中心帧（直接使用reshape而非rearrange）
                skip_reshaped = skip.reshape(B_s, T_s, H_p, W_p, C_s)
                skip_spatial = skip_reshaped[:, t_idx]  # (B, H_p, W_p, C_s)
                skip_spatial = skip_spatial.permute(0, 3, 1, 2)  # (B, C_s, H_p, W_p)
                
                # 上采样到当前特征尺寸
                target_size = x.shape[2:]
                skip_upsampled = F.interpolate(
                    skip_spatial, 
                    size=target_size, 
                    mode='bilinear', 
                    align_corners=False
                )
                
                # 只有通道数匹配时才继续处理
                if skip_upsampled.shape[1] == x.shape[1]:
                    # 应用动态加权
                    skip_gate = self.skip_gates[i](skip_upsampled)  # (B, 1, H, W)
                    
                    # 如果提供了水体频率信息，使用它来调整门控
                    if water_frequency is not None:
                        # 将水体频率调整为当前分辨率，并转为单通道动态权重
                        freq_resized = F.interpolate(
                            water_frequency.unsqueeze(1),  # (B, 1, H, W)
                            size=target_size,
                            mode='bilinear',
                            align_corners=False
                        )
                    
                    # 应用加权的skip connection
                    x = x + skip_gate * skip_upsampled
        
        # Final predictions
        logits = self.final_conv(x)
        confidence = self.confidence(x)
        
        # ------------------------------------------------------------------
        # Numerical stability safeguard: clamp logits to a reasonable range
        # to avoid overflow in subsequent softmax / loss computations.
        # This prevents sporadic NaN/Inf that were observed during training.
        logits = torch.nan_to_num(logits, nan=0.0, posinf=10.0, neginf=-10.0)
        logits = torch.clamp(logits, min=-10.0, max=10.0)
        # ------------------------------------------------------------------
        
        # Ensure output size matches target size
        if logits.shape[-1] != self.img_size:
            logits = F.interpolate(logits, size=(self.img_size, self.img_size),
                                 mode='bilinear', align_corners=False)
            confidence = F.interpolate(confidence, size=(self.img_size, self.img_size),
                                     mode='bilinear', align_corners=False)
        
        return logits, confidence
    
    def forward(self, x, features=None, target_t_ratio=0.5, water_frequency=None):
        """Forward with optional gradient checkpointing"""
        if self.use_checkpoint and self.training:
            # Use non-reentrant checkpointing to avoid nested backward issues
            return torch.utils.checkpoint.checkpoint(
                self._forward_impl, x, features, target_t_ratio, water_frequency, use_reentrant=False
            )
        else:
            return self._forward_impl(x, features, target_t_ratio, water_frequency)
