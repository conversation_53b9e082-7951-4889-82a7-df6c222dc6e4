"""
BCE + Dice Combined Loss Function for Probability Prediction

Combines Binary Cross-Entropy and Dice losses for models outputting probability values.
Both model outputs and labels are probability values in [0, 1] range.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging
from typing import Dict, Tuple

logger = logging.getLogger(__name__)


class Loss(nn.Module):
    """
    Combined BCE + Dice loss for probability prediction
    """
    
    def __init__(self, 
                 bce_weight=0.5,
                 dice_weight=0.5,
                 dice_smooth=1e-6):
        super().__init__()
        
        self.bce_weight = bce_weight
        self.dice_weight = dice_weight
        self.dice_smooth = dice_smooth
        
        # Loss bounds for numerical stability
        self.register_buffer('max_loss_value', torch.tensor(10.0))
        self.register_buffer('min_loss_value', torch.tensor(1e-6))

    def to(self, device):
        """Ensures all buffers and parameters are moved to the specified device"""
        super().to(device)
        return self
    
    def _compute_bce_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                          mask: torch.Tensor) -> torch.Tensor:
        """
        Compute Binary Cross-Entropy loss
        
        Args:
            predictions: (B, H, W) - probability predictions in [0, 1]
            targets: (B, H, W) - ground truth probabilities in [0, 1]
            mask: (H, W) - valid mask
            
        Returns:
            bce_loss: scalar tensor
        """
        device = predictions.device
        valid_mask = mask.bool()
        
        if valid_mask.sum() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Expand mask to match batch dimension
        batch_size = predictions.shape[0]
        mask_expanded = valid_mask.unsqueeze(0).expand(batch_size, -1, -1)
        
        # Apply mask
        pred_valid = predictions[mask_expanded]
        target_valid = targets[mask_expanded]
        
        if pred_valid.numel() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Compute BCE loss
        # Clamp predictions to avoid log(0) or log(1)
        pred_clamped = torch.clamp(pred_valid, min=1e-7, max=1-1e-7)
        bce_loss = F.binary_cross_entropy(pred_clamped, target_valid, reduction='mean')
        
        # Safety check
        bce_loss = torch.clamp(bce_loss, min=self.min_loss_value, max=self.max_loss_value)
        
        if torch.isnan(bce_loss) or torch.isinf(bce_loss):
            logger.warning("NaN/Inf in BCE loss, using fallback")
            return torch.tensor(0.5, device=device, requires_grad=True)
        
        return bce_loss

    def _compute_dice_loss(self, predictions: torch.Tensor, targets: torch.Tensor, 
                          mask: torch.Tensor) -> torch.Tensor:
        """
        Compute Dice loss
        
        Args:
            predictions: (B, H, W) - probability predictions in [0, 1]
            targets: (B, H, W) - ground truth probabilities in [0, 1]
            mask: (H, W) - valid mask
            
        Returns:
            dice_loss: scalar tensor
        """
        device = predictions.device
        valid_mask = mask.bool()
        
        if valid_mask.sum() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Expand mask to match batch dimension
        batch_size = predictions.shape[0]
        mask_expanded = valid_mask.unsqueeze(0).expand(batch_size, -1, -1)
        
        # Apply mask
        pred_valid = predictions[mask_expanded]
        target_valid = targets[mask_expanded]
        
        if pred_valid.numel() == 0:
            return torch.tensor(0.1, device=device, requires_grad=True)
        
        # Compute Dice coefficient
        intersection = (pred_valid * target_valid).sum()
        union = pred_valid.sum() + target_valid.sum()
        
        dice_coeff = (2.0 * intersection + self.dice_smooth) / (union + self.dice_smooth)
        dice_loss = 1.0 - dice_coeff
        
        # Safety check
        dice_loss = torch.clamp(dice_loss, min=0.0, max=1.0)
        
        if torch.isnan(dice_loss) or torch.isinf(dice_loss):
            logger.warning("NaN/Inf in Dice loss, using fallback")
            return torch.tensor(0.5, device=device, requires_grad=True)
        
        return dice_loss

    def forward(self, outputs: Dict, batch: Dict, stage: int = 1) -> Tuple[torch.Tensor, Dict]:
        """
        Compute combined BCE + Dice loss
        
        Args:
            outputs: Model outputs containing 'inpaint' predictions
            batch: Batch data containing ground truth and masks
            stage: Training stage (for compatibility)
        """
        device = batch['ground_truth'].device
        self.to(device)
        
        losses = {}
        metrics = {}
        
        try:
            # Extract data
            target = batch['ground_truth']  # (B, H, W) - probability values
            mask = batch['missing_mask']    # (H, W) - valid mask
            
            # Get predictions
            if isinstance(outputs.get('inpaint'), dict):
                pred_logits = outputs['inpaint']['logits']  # (B, 1, H, W)
            else:
                pred_logits = outputs.get('inpaint', outputs)
            
            # Convert logits to probabilities
            if pred_logits.shape[1] == 2:  # If 2-channel, take first channel
                pred_probabilities = torch.sigmoid(pred_logits[:, 0, :, :])  # (B, H, W)
            else:  # Single channel
                pred_probabilities = torch.sigmoid(pred_logits.squeeze(1))  # (B, H, W)
            
            # Normalize targets to [0, 1] if needed
            if target.max() > 1.0:
                target = target / 100.0
            
            # Input validation
            pred_probabilities = torch.clamp(pred_probabilities, min=0.0, max=1.0)
            target = torch.clamp(target, min=0.0, max=1.0)
            
            # Compute BCE loss
            bce_loss = self._compute_bce_loss(pred_probabilities, target, mask)
            losses['bce'] = bce_loss
            
            # Compute Dice loss
            dice_loss = self._compute_dice_loss(pred_probabilities, target, mask)
            losses['dice'] = dice_loss
            
            # Combine losses with weights
            total_loss = self.bce_weight * bce_loss + self.dice_weight * dice_loss
            
            # Final safety check
            total_loss = torch.clamp(total_loss, 
                                   min=self.min_loss_value, 
                                   max=self.max_loss_value)
            
            if torch.isnan(total_loss) or torch.isinf(total_loss):
                logger.warning("NaN in final loss, using safe fallback")
                total_loss = torch.tensor(1.0, device=device, requires_grad=True)
            
            # Store individual losses in metrics for monitoring
            metrics['bce_loss'] = bce_loss.item()
            metrics['dice_loss'] = dice_loss.item()
            metrics['total_loss'] = total_loss.item()

            return total_loss, metrics
            
        except Exception as e:
            logger.error(f"Error in loss computation: {e}")
            # Return safe fallback loss
            safe_loss = torch.tensor(1.0, device=device, requires_grad=True)
            return safe_loss, {}
        