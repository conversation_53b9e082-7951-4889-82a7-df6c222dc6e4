"""
Complete Water Body Reconstruction Workflow
==========================================

End-to-end workflow for water body reconstruction:
1. Batch inference on NetCDF files
2. Spatial mosaicking and TIF reconstruction

Usage:
python workflow.py /path/to/netcdf --model /path/to/model.pt --output ./results
"""

import os
import sys
import logging
import json
import time
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from prediction.inference_no_cache import NetCDFInferenceEngine
from prediction.restoration import EnhancedNetCDFToTIFRestorer

logger = logging.getLogger(__name__)

class CompleteWorkflowManager:
    """Manages the complete water body reconstruction workflow"""

    def __init__(self, model_weights: str, dataset_directory: str, output_directory: str,
                 config_path: str, device: str = 'cuda:0', batch_size: int = 8,
                 num_workers: int = 4, max_memory_gb: float = 16.0):
        """
        Initialize workflow manager

        Args:
            model_weights: Path to model checkpoint file
            dataset_directory: Directory containing NetCDF files
            output_directory: Output directory for all results
            config_path: Path to configuration file
            device: Device for inference (cuda/cpu)
            batch_size: Batch size for inference
            num_workers: Number of worker processes
            max_memory_gb: Maximum memory usage in GB
        """
        self.model_weights = model_weights
        self.dataset_directory = dataset_directory
        self.output_directory = output_directory
        self.config_path = config_path
        self.device = device
        self.batch_size = batch_size
        self.num_workers = num_workers
        self.max_memory_gb = max_memory_gb
        self.results = {}

        # Setup directories
        self.output_dir = Path(output_directory)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        self.inference_dir = self.output_dir / "inpainted_netcdf"
        self.tif_dir = self.output_dir / "restored_tifs"
        self.test_dir = self.output_dir / "test_results"

        logger.info(f"Initialized CompleteWorkflowManager")
        logger.info(f"  Model weights: {self.model_weights}")
        logger.info(f"  Dataset directory: {self.dataset_directory}")
        logger.info(f"  Output directory: {self.output_dir}")
        logger.info(f"  Inference results: {self.inference_dir}")
        logger.info(f"  TIF outputs: {self.tif_dir}")
    
    def validate_environment(self) -> Dict[str, bool]:
        """Validate that the environment is properly configured"""
        logger.info("Validating environment...")
        
        validation_results = {
            'model_weights_exist': False,
            'dataset_directory_exists': False,
            'output_directory_writable': False,
            'dependencies_available': False
        }
        
        # Check model weights
        model_path = Path(self.model_weights)
        validation_results['model_weights_exist'] = model_path.exists()
        if not validation_results['model_weights_exist']:
            logger.error(f"Model weights not found: {model_path}")

        # Check dataset directory
        dataset_path = Path(self.dataset_directory)
        validation_results['dataset_directory_exists'] = dataset_path.exists()
        if not validation_results['dataset_directory_exists']:
            logger.error(f"Dataset directory not found: {dataset_path}")
        else:
            # Check for NetCDF files
            nc_files = list(dataset_path.glob("*.nc"))
            logger.info(f"Found {len(nc_files)} NetCDF files in dataset directory")
        
        # Check output directory writability
        try:
            test_file = self.output_dir / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
            validation_results['output_directory_writable'] = True
        except Exception as e:
            logger.error(f"Output directory not writable: {e}")
        
        # Check dependencies
        try:
            import torch
            import xarray as xr
            import rasterio
            import numpy as np
            validation_results['dependencies_available'] = True
            logger.info("All required dependencies are available")
        except ImportError as e:
            logger.error(f"Missing dependencies: {e}")
        
        all_valid = all(validation_results.values())
        if all_valid:
            logger.info("Environment validation PASSED")
        else:
            logger.warning("Environment validation FAILED - some components may not work")
        
        return validation_results
    
    def run_inference_phase(self, region_bounds: Optional[Tuple[float, float, float, float]] = None,
                           time_range: Optional[Tuple[str, str]] = None) -> Dict[str, Any]:
        """Run the batch inference phase"""
        logger.info("="*60)
        logger.info("PHASE 1: BATCH INFERENCE")
        logger.info("="*60)

        start_time = time.time()

        try:
            # Create inference engine with configuration
            engine_config = {
                'config_path': self.config_path,
                'device': self.device,
                'batch_size': self.batch_size,
                'num_workers': self.num_workers,
                'max_memory_gb': self.max_memory_gb,
                'checkpoint_path': self.model_weights,
                'output_dir': str(self.inference_dir)
            }

            engine = NetCDFInferenceEngine(self.config_path, **engine_config)
            logger.info("Created inference engine")

            # Run inference using directory and output paths
            inference_results = engine.run_inference(
                nc_dir=self.dataset_directory,
                output_dir=str(self.inference_dir),
                region_bounds=region_bounds,
                time_range=time_range
            )

            # Update results
            inference_results['phase_duration'] = time.time() - start_time
            inference_results['output_directory'] = str(self.inference_dir)

            self.results['inference'] = inference_results

            logger.info(f"Inference phase completed in {inference_results['phase_duration']:.2f} seconds")
            return inference_results

        except Exception as e:
            logger.error(f"Inference phase failed: {e}")
            raise
    
    def run_restoration_phase(self, regions: Optional[Dict[str, Tuple[float, float, float, float]]] = None,
                            time_range: Optional[Tuple[str, str]] = None,
                            **restoration_kwargs) -> Dict[str, Any]:
        """Run the spatial mosaicking and TIF restoration phase"""
        logger.info("="*60)
        logger.info("PHASE 2: SPATIAL MOSAICKING & TIF RESTORATION")
        logger.info("="*60)

        start_time = time.time()

        try:
            # Check if inference results exist
            if not self.inference_dir.exists() or not list(self.inference_dir.glob("*.nc")):
                logger.error(f"No inference results found in {self.inference_dir}")
                logger.error("Please run inference phase first or use --inference_only")
                raise ValueError("Inference results not found")

            # Create enhanced restorer
            restorer = EnhancedNetCDFToTIFRestorer(
                str(self.inference_dir),
                str(self.tif_dir)
            )
            logger.info("Created restorer")

            # Run restoration
            restoration_results = restorer.restore_all(
                regions=regions,
                time_range=time_range,
                num_workers=self.num_workers,
                occurrence_raster=restoration_kwargs.get('occurrence_raster'),
                **{k: v for k, v in restoration_kwargs.items() if k != 'occurrence_raster'}
            )

            # Update results
            restoration_results['phase_duration'] = time.time() - start_time
            restoration_results['source_directory'] = str(self.inference_dir)

            self.results['restoration'] = restoration_results

            logger.info(f"Restoration phase completed in {restoration_results['phase_duration']:.2f} seconds")
            return restoration_results

        except Exception as e:
            logger.error(f"Restoration phase failed: {e}")
            raise
        
    def save_workflow_summary(self) -> None:
        """Save complete workflow summary"""
        summary_path = self.output_dir / "workflow_summary.json"
        
        # Calculate overall statistics
        total_duration = sum(
            phase.get('phase_duration', 0) 
            for phase in self.results.values() 
            if isinstance(phase, dict)
        )
        
        summary = {
            'workflow_completed': True,
            'total_duration_seconds': total_duration,
            'output_directory': str(self.output_dir),
            'phases_completed': list(self.results.keys()),
            'phase_results': self.results,
            'configuration': {
                'model_weights': self.model_weights,
                'dataset_directory': self.dataset_directory,
                'output_directory': self.output_directory,
                'device': self.device,
                'batch_size': self.batch_size,
                'num_workers': self.num_workers,
                'max_memory_gb': self.max_memory_gb
            }
        }
        
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        logger.info(f"Workflow summary saved to {summary_path}")
    
    def run_complete_workflow(self, 
                            regions: Optional[Dict[str, Tuple[float, float, float, float]]] = None,
                            time_range: Optional[Tuple[str, str]] = None,
                            skip_phases: Optional[List[str]] = None,
                            **kwargs) -> Dict[str, Any]:
        """
        Run the complete end-to-end workflow
        
        Args:
            regions: Regions for TIF restoration
            time_range: Time range to process
            skip_phases: List of phases to skip ('inference', 'restoration', 'validation')
            **kwargs: Additional arguments
            
        Returns:
            Complete workflow results
        """
        workflow_start_time = time.time()
        skip_phases = skip_phases or []
        
        logger.info("="*80)
        logger.info("STARTING COMPLETE WATER BODY RECONSTRUCTION WORKFLOW")
        logger.info("="*80)
        
        # Validate environment
        validation_results = self.validate_environment()
        if not all(validation_results.values()):
            logger.warning("Environment validation failed - proceeding with caution")
        
        try:
            # Phase 1: Batch Inference
            if 'inference' not in skip_phases:
                region_bounds = kwargs.get('region_bounds')
                inference_results = self.run_inference_phase(
                    region_bounds=region_bounds,
                    time_range=time_range
                )

                # Check if inference produced results
                if inference_results.get('total_tiles_processed', 0) == 0:
                    logger.warning("No tiles were processed in inference")
            else:
                logger.info("Skipping inference phase")
            
            # Phase 2: Spatial Mosaicking & TIF Restoration
            if 'restoration' not in skip_phases:
                restoration_results = self.run_restoration_phase(
                    regions=regions,
                    time_range=time_range,
                    **kwargs.get('restoration_kwargs', {})
                )
                
                # Check if restoration produced results
                if restoration_results.get('total_time_steps', 0) == 0:
                    logger.warning("No time steps were processed in restoration")
            else:
                logger.info("Skipping restoration phase")
                            
            
            # Save summary
            self.save_workflow_summary()
            
            # Calculate final statistics
            total_workflow_time = time.time() - workflow_start_time
            
            logger.info("="*80)
            logger.info("WORKFLOW COMPLETED SUCCESSFULLY")
            logger.info("="*80)
            logger.info(f"Total workflow time: {total_workflow_time:.2f} seconds")
            logger.info(f"Results saved to: {self.output_dir}")
            
            if 'inference' in self.results:
                logger.info(f"Inference results: {self.inference_dir}")
            if 'restoration' in self.results:
                logger.info(f"TIF outputs: {self.tif_dir}")
            if 'validation' in self.results:
                logger.info(f"Test results: {self.test_dir}")
            
            return self.results
            
        except Exception as e:
            logger.error(f"Workflow failed: {e}")
            # Save partial results
            self.results['workflow_failed'] = True
            self.results['error'] = str(e)
            self.save_workflow_summary()
            raise


def parse_regions_config(regions_str: Optional[str]) -> Optional[Dict[str, Tuple[float, float, float, float]]]:
    """Parse regions configuration from string or file"""
    if not regions_str:
        return None

    if regions_str.endswith('.json'):
        # Load from JSON file
        with open(regions_str, 'r') as f:
            return json.load(f)
    else:
        # Parse as JSON string
        try:
            return json.loads(regions_str)
        except json.JSONDecodeError as e:
            logger.error(f"Invalid regions format: {e}")
            return None







def create_restorer_from_config(netcdf_dir: str,
                               output_dir: str) -> EnhancedNetCDFToTIFRestorer:
    """
    Create an EnhancedNetCDFToTIFRestorer with specified directories.

    Args:
        netcdf_dir: Directory containing NetCDF files
        output_dir: Output directory for TIF files

    Returns:
        Configured EnhancedNetCDFToTIFRestorer instance
    """
    return EnhancedNetCDFToTIFRestorer(netcdf_dir, output_dir)


def main():
    """Main entry point for the complete workflow"""
    parser = argparse.ArgumentParser(
        description='Complete Water Body Reconstruction Workflow',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Full workflow with region filtering:
  python complete_workflow.py /path/to/netcdf --model /path/to/model.pt \\
      --region -80 70 -75 75 --output ./results

  # Inference only (skip TIF restoration):
  python complete_workflow.py /path/to/netcdf --model /path/to/model.pt \\
      --inference_only
        """
    )
    
    # Required arguments
    parser.add_argument('dataset_directory', type=str,
                       help='Directory containing NetCDF files')
    parser.add_argument('--model', '--model_weights', type=str, required=True,
                       help='Path to model checkpoint file')
    
    # Output configuration
    parser.add_argument('--output', '--output_directory', type=str, 
                       default='./workflow_results',
                       help='Output directory for all results')
    parser.add_argument('--config_path', type=str, help='config path')

    # Processing parameters
    parser.add_argument('--batch_size', type=int, default=32,
                       help='Batch size for inference')
    parser.add_argument('--num_workers', type=int, default=4,
                       help='Number of worker processes')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device for inference (cuda/cpu)')
        
    # Region filtering
    parser.add_argument('--region', nargs=4, type=float,
                       metavar=('MIN_LON', 'MIN_LAT', 'MAX_LON', 'MAX_LAT'),
                       help='Geographic region bounds for processing')
    parser.add_argument('--regions', type=str,
                       help='JSON file or string defining multiple regions')
    
    # Time filtering
    parser.add_argument('--time_range', nargs=2, metavar=('START', 'END'),
                       help='Time range for processing (YYYY-MM-DD)')
    
    # Workflow control
    parser.add_argument('--inference_only', action='store_true',
                       help='Run inference only, skip TIF restoration')
    parser.add_argument('--restoration_only', action='store_true',
                       help='Run TIF restoration only, skip inference')
    parser.add_argument('--test_only', action='store_true',
                       help='Run test workflow only')
        
    # TIF restoration parameters
    parser.add_argument('--merge_method', choices=['average', 'first', 'last', 'max'],
                       default='average', help='Method for merging overlapping areas')
    parser.add_argument('--compression', choices=['lzw', 'deflate', 'none'],
                       default='lzw', help='Output compression method')
    parser.add_argument('--occurrence_raster', type=str,
                       help='Path to occurrence raster for validation')

    # Memory and performance
    parser.add_argument('--max_memory_gb', type=float, default=16.0,
                       help='Maximum memory usage in GB')
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.INFO
    logging.basicConfig(
        level=log_level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('complete_workflow.log'),
            logging.StreamHandler()
        ]
    )
    
    # Parse regions for TIF restoration
    regions = None
    if args.regions:
        regions = parse_regions_config(args.regions)
    elif args.region:
        regions = {"specified_region": tuple(args.region)}

    # Create workflow manager with direct parameters
    workflow_manager = CompleteWorkflowManager(
        model_weights=args.model,
        dataset_directory=args.dataset_directory,
        output_directory=args.output,
        config_path=args.config_path,
        device=args.device,
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        max_memory_gb=args.max_memory_gb
    )
    
    try:
        # Determine phases to skip
        skip_phases = []
        if args.inference_only:
            skip_phases.extend(['restoration', 'validation'])
        elif args.restoration_only:
            skip_phases.extend(['inference', 'validation'])
        elif args.test_only:
            skip_phases.extend(['inference', 'restoration'])
        
        # Run workflow
        results = workflow_manager.run_complete_workflow(
            regions=regions,
            time_range=tuple(args.time_range) if args.time_range else None,
            skip_phases=skip_phases,
            region_bounds=tuple(args.region) if args.region else None,
            restoration_kwargs={
                'merge_method': args.merge_method,
                'compression': args.compression,
                'occurrence_raster': args.occurrence_raster
            }
        )
        
        # Print summary
        print("\\n" + "="*80)
        print("WORKFLOW SUMMARY")
        print("="*80)
        print(json.dumps(results, indent=2, default=str))
        
    except Exception as e:
        logger.error(f"Workflow execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()