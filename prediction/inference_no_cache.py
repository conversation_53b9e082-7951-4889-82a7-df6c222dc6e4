"""
NetCDF数据集推理模块 - 优化版本

只在缺失区域写入推理结果
"""


import shutil
import numpy as np
import pandas as pd
import xarray as xr
import torch
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union, Any


from tqdm import tqdm
import logging
from netCDF4 import Dataset as NcDataset

import time
import gc
import warnings
from concurrent.futures import ProcessPoolExecutor, as_completed
from multiprocessing import cpu_count

import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap
warnings.filterwarnings('ignore')

# Add project root to path
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from model.model_v20 import create_swin_water_net
from data.dataset import DataProcessor, MODEL_VALUES, JRC_VALUES
from configs import get_config, Config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def _scan_single_file(nc_file: Path, missing_threshold: float, time_range: Optional[Tuple[str, str]] = None) -> List[Dict[str, Any]]:
    """
    扫描单个NetCDF文件的辅助函数 - 用于多进程处理

    Args:
        nc_file: NetCDF文件路径
        missing_threshold: 缺失数据阈值
        time_range: 时间范围过滤 (start_date, end_date)

    Returns:
        该文件中所有需要推理的tile样本列表
    """
    file_samples = []

    try:
        with xr.open_dataset(nc_file, decode_times=True, mask_and_scale=False) as ds:
            # 快速预检查：如果没有mean_water_frequency或全为NaN，跳过
            if 'mean_water_frequency' not in ds.data_vars:
                return file_samples

            mean_freq_array = ds.mean_water_frequency.values
            if np.all(np.isnan(mean_freq_array)):
                return file_samples

            # 找到所有非NaN的位置
            valid_mask = ~np.isnan(mean_freq_array)
            valid_indices = np.where(valid_mask)

            if len(valid_indices[0]) == 0:
                return file_samples

            # 时间范围过滤
            time_indices_to_process = None
            if time_range is not None:
                try:
                    # 获取时间坐标
                    time_coords = pd.to_datetime(ds.time.values)
                    start_time, end_time = pd.to_datetime(time_range[0]), pd.to_datetime(time_range[1])

                    # 找到在时间范围内的时间索引
                    valid_time_mask = (time_coords >= start_time) & (time_coords <= end_time)
                    time_indices_to_process = np.where(valid_time_mask)[0]

                    if len(time_indices_to_process) == 0:
                        logger.debug(f"No time points in range {time_range} for file {nc_file.name}")
                        return file_samples

                    logger.debug(f"File {nc_file.name}: filtering to {len(time_indices_to_process)}/{len(time_coords)} time points")
                except Exception as e:
                    logger.warning(f"Error applying time filter to {nc_file}: {e}, processing all time points")
                    time_indices_to_process = None

            # 批量读取missing_proportion数据
            missing_proportion_data = ds.missing_proportion.values

            # 完全向量化处理所有有效tiles
            valid_x_coords = valid_indices[0]
            valid_y_coords = valid_indices[1]

            # 提取所有有效tiles的missing_ratios (N_tiles, T)
            valid_missing_ratios = missing_proportion_data[valid_x_coords, valid_y_coords, :]

            # 向量化找到所有满足条件的位置 (N_tiles, T)
            valid_time_mask = valid_missing_ratios > missing_threshold

            # 如果指定了时间范围，进一步过滤时间维度
            if time_indices_to_process is not None:
                time_filter_mask = np.zeros(valid_missing_ratios.shape[1], dtype=bool)
                time_filter_mask[time_indices_to_process] = True
                valid_time_mask = valid_time_mask & time_filter_mask[None, :]

            # 找到所有满足条件的(tile_idx, time_idx)对
            tile_indices, time_indices = np.where(valid_time_mask)

            # 批量创建样本字典
            if len(tile_indices) > 0:
                nc_file_str = str(nc_file)
                file_samples.extend([
                    {
                        'nc_file': nc_file_str,
                        'idx_x': int(valid_x_coords[tile_idx]),
                        'idx_y': int(valid_y_coords[tile_idx]),
                        'center_time_idx': int(time_idx),
                        'missing_ratio': float(valid_missing_ratios[tile_idx, time_idx])
                    }
                    for tile_idx, time_idx in zip(tile_indices, time_indices)
                ])

    except Exception as e:
        logger.error(f"Error scanning file {nc_file}: {e}")

    return file_samples

def extract_coords_from_filename(filename: str) -> Optional[Tuple[float, float]]:
    """从NetCDF文件名直接解析经纬度坐标"""
    try:
        # 文件名格式: JRC_window_{lon}_{lat}.nc
        basename = Path(filename).stem
        if basename.startswith('JRC_window_'):
            parts = basename.split('_')
            if len(parts) >= 4:
                lon_str = parts[2]
                lat_str = parts[3]
                return float(lon_str), float(lat_str)
        elif basename.startswith('inpainted_'):
            parts = basename.split('_')
            if len(parts) >= 5:
                lon_str = parts[3]
                lat_str = parts[4]
                return float(lon_str), float(lat_str)
        else:
            logger.error(f"Unknown filename format: {filename}")
            return None
    except Exception as e:
        logger.debug(f"Could not extract coordinates from filename {filename}: {e}")
    return None

def find_nc_files(nc_dir: str, region_bounds: Optional[Tuple[float, float, float, float]] = None) -> List[Path]:
    """查找NetCDF文件，直接从文件名读取经纬度 - 向量化版本"""
    nc_files = list(Path(nc_dir).glob("*.nc"))

    if region_bounds is None:
        return nc_files

    # 向量化根据区域边界过滤文件
    min_lon, min_lat, max_lon, max_lat = region_bounds

    # 向量化批量解析所有文件的坐标
    coords_list = [extract_coords_from_filename(str(nc_file)) for nc_file in nc_files]

    # 向量化过滤有效坐标的文件 - 使用numpy布尔索引
    valid_mask = np.array([coords is not None for coords in coords_list])
    valid_files = np.array(nc_files)[valid_mask]
    valid_coords = [coords for coords in coords_list if coords is not None]

    if len(valid_coords) == 0:
        logger.info("No files with valid coordinates found")
        return []




    # 向量化坐标检查
    lons = np.array([coord[0] for coord in valid_coords])
    lats = np.array([coord[1] for coord in valid_coords])

    # 向量化边界检查
    in_bounds_mask = (
        (lons >= min_lon) & (lons <= max_lon) &
        (lats >= min_lat) & (lats <= max_lat)
    )

    # 选择在边界内的文件
    selected_files = [valid_files[i] for i in np.where(in_bounds_mask)[0]]

    # 批量记录调试信息
    if logger.isEnabledFor(logging.DEBUG):
        for i, in_bounds in enumerate(in_bounds_mask):
            if in_bounds:
                logger.debug(f"File {valid_files[i].name} is within region bounds: {lons[i]}, {lats[i]}")

    logger.info(f"Found {len(selected_files)} NetCDF files in region")
    return selected_files

class InferenceDataset(Dataset):
    """推理数据集，用于管理NetCDF文件中需要推理的tiles"""

    def __init__(self, nc_files: List[Path], window_size: int = 48, missing_threshold: float = 0.0,
                 scan_method: str = 'auto', time_range: Optional[Tuple[str, str]] = None):
        """
        初始化推理数据集

        Args:
            nc_files: NetCDF文件列表
            window_size: 滑动窗口大小
            missing_threshold: 缺失数据阈值，只处理缺失比例大于此值的tiles
            scan_method: 扫描方法 ('auto', 'serial', 'parallel')
                - 'auto': 根据文件数量自动选择最优方法
                - 'serial': 串行扫描（原始方法）
                - 'parallel': 多进程并行扫描
            time_range: 时间范围过滤 (start_date, end_date)
        """
        self.nc_files = nc_files
        self.window_size = window_size
        self.missing_threshold = missing_threshold
        self.time_range = time_range
        self.data_processor = DataProcessor()



        # 扫描所有需要推理的tiles
        self.tile_samples = []

        # 根据扫描方法选择合适的扫描函数
        if scan_method == 'auto':
            if len(nc_files) > 10:
                scan_method = 'parallel'
            else:
                scan_method = 'serial'

        logger.info(f"Using {scan_method} scanning method for {len(nc_files)} files")

        if scan_method == 'parallel':
            self._scan_tiles_parallel()
        else:
            self._scan_tiles()

        logger.info(f"Initialized InferenceDataset with {len(self.tile_samples)} tile samples")

    def _scan_tiles_parallel(self):
        """并行扫描所有NetCDF文件 - 高性能版本"""
        if len(self.nc_files) <= 2:
            # 文件数量少时直接使用串行版本
            return self._scan_tiles()

        logger.info(f"Starting parallel scanning of {len(self.nc_files)} NetCDF files")

        # 使用多进程并行处理文件
        max_workers = min(cpu_count() - 1, len(self.nc_files), 8)  # 限制最大进程数

        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有文件的扫描任务
            future_to_file = {
                executor.submit(_scan_single_file, nc_file, self.missing_threshold, self.time_range): nc_file
                for nc_file in self.nc_files
            }

            # 收集结果
            for future in tqdm(as_completed(future_to_file),
                             total=len(self.nc_files),
                             desc="Scanning NetCDF files"):
                nc_file = future_to_file[future]
                try:
                    file_samples = future.result()
                    self.tile_samples.extend(file_samples)
                except Exception as e:
                    logger.error(f"Error scanning file {nc_file}: {e}")
                    continue

    def _scan_tiles(self):
        """扫描所有NetCDF文件，找到需要推理的tiles - 优化版本"""
        for nc_file in self.nc_files:
            try:
                with xr.open_dataset(nc_file, decode_times=True, mask_and_scale=False) as ds:
                    # 获取mean_water_frequency来判断哪些tiles是有效的
                    mean_freq_array = ds.mean_water_frequency.values  # shape: (idx_x, idx_y)

                    # 找到所有非NaN的位置，这些位置需要推理
                    valid_mask = ~np.isnan(mean_freq_array)
                    valid_indices = np.where(valid_mask)

                    if len(valid_indices[0]) == 0:
                        continue  # 没有有效的tiles，跳过这个文件

                    # 时间范围过滤
                    time_indices_to_process = None
                    if self.time_range is not None:
                        try:
                            # 获取时间坐标
                            time_coords = pd.to_datetime(ds.time.values)
                            start_time, end_time = pd.to_datetime(self.time_range[0]), pd.to_datetime(self.time_range[1])

                            # 找到在时间范围内的时间索引
                            valid_time_mask = (time_coords >= start_time) & (time_coords <= end_time)
                            time_indices_to_process = np.where(valid_time_mask)[0]

                            if len(time_indices_to_process) == 0:
                                logger.debug(f"No time points in range {self.time_range} for file {nc_file.name}")
                                continue

                            logger.debug(f"File {nc_file.name}: filtering to {len(time_indices_to_process)}/{len(time_coords)} time points")
                        except Exception as e:
                            logger.warning(f"Error applying time filter to {nc_file}: {e}, processing all time points")
                            time_indices_to_process = None

                    # 完全向量化处理所有有效tiles
                    valid_x_coords = valid_indices[0]
                    valid_y_coords = valid_indices[1]

                    # 使用向量化操作批量处理
                    missing_proportion_data = ds.missing_proportion.values  # shape: (idx_x, idx_y, time)

                    # 提取所有有效tiles的missing_ratios (N_tiles, T)
                    valid_missing_ratios = missing_proportion_data[valid_x_coords, valid_y_coords, :]

                    # 向量化找到所有满足条件的位置 (N_tiles, T)
                    valid_time_mask = valid_missing_ratios > self.missing_threshold

                    # 如果指定了时间范围，进一步过滤时间维度
                    if time_indices_to_process is not None:
                        time_filter_mask = np.zeros(valid_missing_ratios.shape[1], dtype=bool)
                        time_filter_mask[time_indices_to_process] = True
                        valid_time_mask = valid_time_mask & time_filter_mask[None, :]

                    # 找到所有满足条件的(tile_idx, time_idx)对
                    tile_indices, time_indices = np.where(valid_time_mask)

                    # 批量创建样本字典
                    if len(tile_indices) > 0:
                        nc_file_str = str(nc_file)
                        self.tile_samples.extend([
                            {
                                'nc_file': nc_file_str,
                                'idx_x': int(valid_x_coords[tile_idx]),
                                'idx_y': int(valid_y_coords[tile_idx]),
                                'center_time_idx': int(time_idx),
                                'missing_ratio': float(valid_missing_ratios[tile_idx, time_idx])
                            }
                            for tile_idx, time_idx in zip(tile_indices, time_indices)
                        ])

            except Exception as e:
                logger.error(f"Error scanning file {nc_file}: {e}")
                continue

    def __len__(self) -> int:
        return len(self.tile_samples)

    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        获取单个样本数据

        Args:
            idx: 样本索引

        Returns:
            包含推理所需数据的字典
        """
        sample_info = self.tile_samples[idx]

        try:
            return self._load_sample_data(sample_info)
        except Exception as e:
            logger.error(f"Error loading sample {idx}: {e}")
            return self._create_dummy_sample()

    def _get_dataset_handle(self, nc_file: str) -> xr.Dataset:
        """直接打开Dataset句柄"""
        return xr.open_dataset(nc_file, decode_times=False, mask_and_scale=False)

    def _get_occ_data(self, ds: xr.Dataset, idx_x: int, idx_y: int) -> np.ndarray:
        """直接获取occurrence数据"""
        return ds.occ_data.isel(idx_x=idx_x, idx_y=idx_y).values

    def _load_sample_data(self, sample_info: Dict[str, Any]) -> Dict[str, Any]:
        """加载单个样本的数据"""
        nc_file = sample_info['nc_file']
        idx_x = sample_info['idx_x']
        idx_y = sample_info['idx_y']
        center_time_idx = sample_info['center_time_idx']

        with self._get_dataset_handle(nc_file) as ds:
            # 向量化时间窗口计算
            T = int(ds.sizes['time'])
            half_seq = self.window_size // 2

            # 使用numpy的clip函数进行向量化边界处理
            start_time = np.clip(center_time_idx - half_seq, 0, T)
            end_time = np.clip(center_time_idx + half_seq, 0, T)

            # 向量化窗口大小调整
            if end_time - start_time < self.window_size:
                if start_time == 0:
                    end_time = min(T, self.window_size)
                else:
                    start_time = max(0, end_time - self.window_size)

            window_data = ds.data.isel(idx_x=idx_x, idx_y=idx_y, time=slice(start_time, end_time)).values
            center_frame_idx = center_time_idx - start_time

            # 直接获取occurrence数据
            occ_data = self._get_occ_data(ds, idx_x, idx_y)

        # 向量化预处理数据
        processed_window_data, window_missing_mask = self.data_processor.preprocess_jrc_data_with_mask(window_data)
        normalized_occ = self.data_processor.normalize_occurrence_data(occ_data)

        # 向量化构建两通道输入数据
        water_value = MODEL_VALUES['water']
        missing_value = MODEL_VALUES['missing']

        # 使用向量化比较和类型转换
        water_channel = (processed_window_data == water_value).astype(np.float32)
        missing_channel = (processed_window_data == missing_value).astype(np.float32)
        two_channel_data = np.stack([water_channel, missing_channel], axis=1)  # (T, 2, H, W)

        return {
            'input_sequence': torch.from_numpy(two_channel_data).float(),
            'occurrence': torch.from_numpy(normalized_occ).float(),
            'center_frame_idx': torch.tensor(center_frame_idx).long(),
            'missing_mask': torch.from_numpy(window_missing_mask[center_frame_idx]).bool(),
            'sample_info': sample_info,
        }

    def _create_sliding_window_data(self, full_data: np.ndarray,
                                   center_time_idx: int, window_size: int) -> Tuple[np.ndarray, int]:
        """创建滑动窗口数据，参考dataset.py的边界处理逻辑"""
        time_size = full_data.shape[0]

        # 计算以center_time_idx为中心的时间窗口边界
        half_seq = window_size // 2
        start_time = max(0, center_time_idx - half_seq)
        end_time = min(time_size, center_time_idx + half_seq)

        # 确保我们得到确切的window_size帧
        if end_time - start_time < window_size:
            if start_time == 0:
                end_time = min(time_size, window_size)
            else:
                start_time = max(0, end_time - window_size)

        # 提取窗口数据
        window_data = full_data[start_time:end_time]

        # 计算窗口内的中心帧索引
        center_frame_idx = center_time_idx - start_time
        assert 0 <= center_frame_idx < window_size, f"Invalid center frame index: {center_frame_idx}"

        return window_data, center_frame_idx

    def _create_dummy_sample(self) -> Dict[str, Any]:
        """创建虚拟样本用于错误恢复"""
        T, H, W = self.window_size, 256, 256

        return {
            'input_sequence': torch.zeros(T, 2, H, W).float(),
            'occurrence': torch.zeros(H, W).float(),
            'center_frame_idx': torch.tensor(0).long(),
            'missing_mask': torch.zeros(H, W).bool(),
            'sample_info': {'nc_file': '', 'idx_x': 0, 'idx_y': 0, 'center_time_idx': 0},
        }


class InferenceVisualizationModule:
    """推理过程中的可视化模块"""

    def __init__(self, output_dir: Path, enabled: bool = True):
        """
        初始化可视化模块

        Args:
            output_dir: 输出目录
            enabled: 是否启用可视化
        """
        self.output_dir = Path(output_dir)
        self.enabled = enabled
        self.visualization_dir = self.output_dir / "visualization"

        if self.enabled:
            self.visualization_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Visualization module initialized, output dir: {self.visualization_dir}")

    def visualize_first_batch(self, batch: Dict[str, Any], predictions: np.ndarray,
                            num_samples: int = 5) -> None:
        """
        可视化第一个batch中的随机样本

        Args:
            batch: 批次数据
            predictions: 模型预测结果 (B, H, W) - 0-100概率值
            num_samples: 可视化样本数量
        """
        if not self.enabled:
            return

        try:
            batch_size = predictions.shape[0]
            actual_num_samples = min(num_samples, batch_size)

            # 向量化随机选择样本索引
            sample_indices = np.random.choice(batch_size, size=actual_num_samples, replace=False)

            # 创建子图 - 4列：输入、预测、occurrence、从NC文件读取的结果
            fig, axes = plt.subplots(actual_num_samples, 4, figsize=(16, 4 * actual_num_samples))
            if actual_num_samples == 1:
                axes = axes.reshape(1, -1)

            fig.suptitle('First Batch Inference Visualization with NC Verification', fontsize=16)

            # 创建颜色映射（参考evaluation/visualization）
            colors_yellow_blue = [(1, 1, 0), (0, 0, 1)]
            n_bins = 100
            yellow_blue_cmap = LinearSegmentedColormap.from_list("custom_yb", colors_yellow_blue, N=n_bins)

            for i, sample_idx in enumerate(sample_indices):
                self._visualize_single_sample(axes[i], batch, predictions, sample_idx, yellow_blue_cmap, self.output_dir)

            # 保存图像
            plt.tight_layout()
            save_path = self.visualization_dir / "first_batch_visualization.png"
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            logger.info(f"First batch visualization saved to: {save_path}")

        except Exception as e:
            logger.error(f"Error in first batch visualization: {e}")
            plt.close('all')

    def _visualize_single_sample(self, axes, batch: Dict[str, Any], predictions: np.ndarray,
                               sample_idx: int, yellow_blue_cmap, output_dir: Path) -> None:
        """
        可视化单个样本（参考evaluation/visualization风格）

        Args:
            axes: matplotlib轴对象
            batch: 批次数据
            predictions: 预测结果
            sample_idx: 样本索引
            yellow_blue_cmap: 颜色映射
            output_dir: 输出目录，用于查找保存的NC文件
        """
        # 提取数据
        input_seq = batch['input_sequence'][sample_idx].cpu().numpy()  # (T, 2, H, W)
        occurrence = batch['occurrence'][sample_idx].cpu().numpy()  # (H, W)
        center_frame_idx = batch['center_frame_idx'][sample_idx].item()
        prediction = predictions[sample_idx] / 100.0  # 转换为0-1概率

        # 获取中心帧的输入数据
        center_input = input_seq[center_frame_idx]  # (2, H, W)

        # 准备输入可视化（参考evaluation/visualization的prepare_input_visualization）
        input_vis = self._prepare_input_visualization(center_input)

        # 获取样本信息
        if isinstance(batch['sample_info'], list):
            sample_info = batch['sample_info'][sample_idx]
        else:
            sample_info = {k: v[sample_idx] if isinstance(v, (list, torch.Tensor)) else v
                          for k, v in batch['sample_info'].items()}

        nc_file = Path(sample_info['nc_file']).name
        idx_x, idx_y = sample_info['idx_x'], sample_info['idx_y']
        center_time = sample_info['center_time_idx']

        # 1. 向量化输入图像处理
        H, W = input_vis.shape[:2]
        input_rgb = np.ones((H, W, 3), dtype=np.float32)

        # 向量化掩码计算
        water_mask = input_vis[:, :, 0] > 0.5  # 水体掩码
        missing_mask = input_vis[:, :, 2] > 0.5  # 缺失掩码

        # 向量化颜色赋值
        input_rgb[water_mask] = [0, 0, 1]  # 蓝色表示水体
        input_rgb[missing_mask] = [0.7, 0.7, 0.7]  # 灰色表示缺失

        axes[0].imshow(input_rgb)
        title = f'Sample {sample_idx} - Input\n{nc_file}\nTile({idx_x},{idx_y}) - Time({center_time})'
        axes[0].set_title(title, fontsize=10)
        axes[0].axis('off')

        # 2. 预测结果（使用黄蓝色映射）
        im1 = axes[1].imshow(prediction, cmap=yellow_blue_cmap, vmin=0, vmax=1)
        axes[1].set_title('Prediction')
        axes[1].axis('off')
        plt.colorbar(im1, ax=axes[1], fraction=0.046)

        # 3. 水体出现频率（使用viridis色映射）
        im2 = axes[2].imshow(occurrence, cmap='viridis', vmin=0, vmax=1)
        axes[2].set_title('Water Occurrence')
        axes[2].axis('off')
        plt.colorbar(im2, ax=axes[2], fraction=0.046)

        # 4. 从保存的NC文件读取的结果（用于验证）
        nc_result = self._read_nc_result(output_dir, sample_info, center_time)
        if nc_result is not None:
            im3 = axes[3].imshow(nc_result, cmap=yellow_blue_cmap, vmin=0, vmax=1)
            axes[3].set_title('NC File Result\n(Verification)')
            axes[3].axis('off')
            plt.colorbar(im3, ax=axes[3], fraction=0.046)

            # 计算差异
            diff = np.abs(prediction - nc_result)
            max_diff = np.max(diff)
            mean_diff = np.mean(diff)
            axes[3].text(0.02, 0.98, f'Max Diff: {max_diff:.4f}\nMean Diff: {mean_diff:.4f}',
                        transform=axes[3].transAxes, va='top', ha='left',
                        bbox=dict(boxstyle='round', facecolor='white', alpha=0.8),
                        fontsize=8)
        else:
            # 如果无法读取NC文件，显示提示
            axes[3].text(0.5, 0.5, 'NC file not found\nor not yet saved',
                        transform=axes[3].transAxes, ha='center', va='center',
                        fontsize=12, color='red')
            axes[3].set_title('NC File Result\n(Not Available)')
            axes[3].axis('off')

    def _prepare_input_visualization(self, input_tensor: np.ndarray) -> np.ndarray:
        """
        准备输入可视化数据（参考evaluation/visualization）

        Args:
            input_tensor: 输入张量 (2, H, W) - [water_channel, missing_channel]

        Returns:
            可视化数组 (H, W, 3)
        """
        # 向量化可视化数据准备
        H, W = input_tensor.shape[1:]
        vis = np.zeros((3, H, W), dtype=np.float32)

        # 向量化通道赋值
        water_channel = input_tensor[0]
        vis[0] = water_channel  # 水体通道
        vis[1] = 1.0 - water_channel  # 非水体通道（向量化计算）
        vis[2] = input_tensor[1]  # 缺失通道

        return vis.transpose(1, 2, 0)  # (H, W, 3)

    def _read_nc_result(self, output_dir: Path, sample_info: Dict[str, Any], center_time: int) -> Optional[np.ndarray]:
        """
        从保存的NC文件中读取对应的预测结果

        Args:
            output_dir: 输出目录
            sample_info: 样本信息
            center_time: 中心时间索引

        Returns:
            预测结果数组 (H, W) 或 None（如果读取失败）
        """
        try:
            # 构建输出文件路径
            nc_file_path = Path(sample_info['nc_file'])
            output_nc_path = output_dir / f"inpainted_{nc_file_path.name}"

            if not output_nc_path.exists():
                logger.debug(f"Output NC file not found: {output_nc_path}")
                return None

            # 读取数据
            import xarray as xr
            with xr.open_dataset(output_nc_path, decode_times=False, mask_and_scale=False) as ds:
                idx_x = sample_info['idx_x']
                idx_y = sample_info['idx_y']

                # 检查数据变量是否存在
                if 'data' not in ds.data_vars:
                    logger.debug(f"'data' variable not found in {output_nc_path}")
                    return None

                # 获取数据维度信息
                data_dims = ds.data.dims
                data_shape = ds.data.shape
                logger.debug(f"NC file dimensions: {data_dims}, shape: {data_shape}")

                # 检查索引范围
                if 'idx_x' in data_dims and idx_x >= ds.sizes.get('idx_x', 0):
                    logger.debug(f"idx_x out of range: {idx_x} >= {ds.sizes.get('idx_x', 0)}")
                    return None
                if 'idx_y' in data_dims and idx_y >= ds.sizes.get('idx_y', 0):
                    logger.debug(f"idx_y out of range: {idx_y} >= {ds.sizes.get('idx_y', 0)}")
                    return None
                if 'time' in data_dims and center_time >= ds.sizes.get('time', 0):
                    logger.debug(f"time out of range: {center_time} >= {ds.sizes.get('time', 0)}")
                    return None

                # 根据实际的数据结构读取数据
                tile_data = ds.data.isel(idx_x=idx_x, idx_y=idx_y, time=center_time).values

                # 转换为0-1概率值
                if tile_data.dtype == np.uint8 and np.max(tile_data) > 1:
                    tile_data = tile_data.astype(np.float32) / 100.0
                elif tile_data.dtype != np.float32 and np.max(tile_data) <= 1:
                    tile_data = tile_data.astype(np.float32)

                return tile_data

        except Exception as e:
            logger.debug(f"Failed to read NC result: {e}")
            return None


class NetCDFInferenceEngine:
    """NetCDF推理引擎"""

    def __init__(self, config: Optional[Union[str, Config]] = None, **overrides):
        """
        初始化推理引擎

        Args:
            config: 配置文件路径或Config对象
            **overrides: 配置覆盖参数
        """
        # 处理不同类型的配置输入
        if isinstance(config, str):
            # 配置文件路径
            self.cfg = get_config(config)
        elif isinstance(config, Config):
            # Config对象
            self.cfg = config
        else:
            # 使用默认配置
            self.cfg = get_config('/home/<USER>/Water/configs/config_v20.yaml')

        # 应用覆盖参数
        for key, value in overrides.items():
            if '.' in key:
                self.cfg.set(key, value)
            else:
                # 假设是inference配置
                self.cfg.set(f'inference.{key}', value)

        self.model = None
        self.device = torch.device(self.cfg.get('inference.device', 'cuda:0'))
        self.inference_dir = Path(self.cfg.get('inference.output_dir', './results'))
        # 初始化数据处理器
        self.data_processor = DataProcessor()




        # 初始化可视化模块
        visualization_enabled = self.cfg.get('inference.visualization.enabled', True)
        self.visualization_module = InferenceVisualizationModule(
            self.inference_dir, enabled=visualization_enabled
        )

        logger.info(f"Initialized inference engine with device: {self.device}")
        logger.info(f"Using configuration: batch_size={self.batch_size}, "
                   f"num_workers={self.num_workers}, "
                   f"missing_threshold={self.missing_threshold}")

    # 配置属性
    @property
    def model_checkpoint(self) -> str:
        return self.cfg.get('inference.checkpoint_path', './checkpoints/best_model.pth')

    @property
    def batch_size(self) -> int:
        return self.cfg.get('inference.batch_size', 16)

    @property
    def num_workers(self) -> int:
        return self.cfg.get('inference.num_workers', 4)

    @property
    def missing_threshold(self) -> float:
        return self.cfg.get('inference.missing_threshold', 0.0)


    @property
    def sliding_window_size(self) -> int:
        return self.cfg.get('inference.sliding_window_size', 48)

    @property
    def prefetch_buffer(self) -> int:
        return self.cfg.get('inference.prefetch_buffer', 2)



    @property
    def time_range(self) -> Optional[Tuple[str, str]]:
        """获取时间范围配置"""
        return self.cfg.get('inference.time_range')

    def load_model(self):
        """加载模型：严格参考 efficient_batch_inference.py 的方式"""
        if self.model is not None:
            return
        try:
            checkpoint = None
            # 优先安全方式加载（PyTorch 2.6+ 支持 weights_only=True）
            try:
                try:
                    if hasattr(torch, 'serialization') and hasattr(torch.serialization, 'add_safe_globals'):
                        torch.serialization.add_safe_globals([get_config])
                        logger.info("Registered get_config in torch.serialization.safe_globals for secure loading")
                except Exception as sg_err:
                    logger.debug(f"Failed to register safe globals: {sg_err}")

                checkpoint = torch.load(self.model_checkpoint, map_location=self.device, weights_only=True)
                logger.info("Checkpoint loaded with weights_only=True (safe mode)")
            except TypeError:
                # 兼容旧版 PyTorch
                checkpoint = torch.load(self.model_checkpoint, map_location=self.device)
                logger.info("Checkpoint loaded without weights_only (older PyTorch)")
            except Exception:
                # 最后兜底（信任 checkpoint 来源）
                logger.info("weights_only=True load failed; falling back to weights_only=False due to trusted checkpoint")
                checkpoint = torch.load(self.model_checkpoint, map_location=self.device, weights_only=False)

            # 使用配置创建模型
            model_config = self.cfg

            # 创建模型
            self.model = create_swin_water_net(model_config)

            # 加载权重（支持 dict 或直接 state_dict）
            state_dict = checkpoint.get('model_state_dict', checkpoint) if isinstance(checkpoint, dict) else checkpoint
            if isinstance(state_dict, dict) and any(k.startswith('module.') for k in state_dict.keys()):
                state_dict = {k.replace('module.', ''): v for k, v in state_dict.items()}

            missing_keys, unexpected_keys = self.model.load_state_dict(state_dict, strict=False)
            if missing_keys:
                logger.warning(f"Missing keys in model state_dict: {missing_keys}")
            if unexpected_keys:
                logger.warning(f"Unexpected keys in model state_dict: {unexpected_keys}")

            # 设备与性能设置
            self.model.to(self.device)

            # 确保所有子模块都在正确的设备上
            for name, module in self.model.named_modules():
                if hasattr(module, 'weight') and module.weight is not None:
                    if module.weight.device != self.device:
                        logger.warning(f"Moving {name} weights from {module.weight.device} to {self.device}")
                        module.to(self.device)
                if hasattr(module, 'bias') and module.bias is not None:
                    if module.bias.device != self.device:
                        logger.warning(f"Moving {name} bias from {module.bias.device} to {self.device}")
                        module.to(self.device)

            try:
                self.model = self.model.to(memory_format=torch.channels_last)
            except Exception:
                pass
            if self.device.type == 'cuda':
                try:
                    torch.backends.cudnn.benchmark = True
                except Exception:
                    pass
            self.model.eval()

            logger.info(f"Model loaded successfully from {self.model_checkpoint}")
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise

    def run_inference_with_dataloader(self, nc_files: List[Path], time_range: Optional[Tuple[str, str]] = None) -> Dict[str, Any]:
        """
        使用Dataset和DataLoader进行推理

        Args:
            nc_files: NetCDF文件列表
            time_range: 时间范围过滤 (start_date, end_date)

        Returns:
            推理结果字典
        """
        # 创建推理数据集 - 使用自动选择的最优扫描方法
        # 优先使用传入的time_range参数，如果没有则使用配置中的time_range
        effective_time_range = time_range if time_range is not None else self.time_range

        dataset = InferenceDataset(
            nc_files=nc_files,
            window_size=self.sliding_window_size,
            missing_threshold=self.missing_threshold,
            scan_method='auto',  # 自动选择最优扫描方法
            time_range=effective_time_range
        )

        if len(dataset) == 0:
            logger.warning("No samples found for inference")
            return {}

        # 创建数据加载器
        dataloader = DataLoader(
            dataset,
            batch_size=self.batch_size,
            num_workers=self.num_workers,
            shuffle=False,
            pin_memory=True,
            drop_last=False,
            persistent_workers=(self.num_workers > 0)
        )

        # 加载模型
        self.load_model()

        # 按文件分组的结果缓存
        file_results_cache: Dict[Path, Dict[str, Any]] = {}
        total_tiles_processed = 0
        total_pixels_inpainted = 0

        # 保存第一个batch的信息用于后续可视化
        first_batch_info = None

        logger.info(f"Starting inference on {len(dataset)} samples using DataLoader")

        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(dataloader, desc="Inference")):
                try:
                    # 将数据移到设备
                    batch_device = {
                        'input_sequence': batch['input_sequence'].to(self.device, non_blocking=True),
                        'occurrence': batch['occurrence'].to(self.device, non_blocking=True),
                        'center_frame_idx': batch['center_frame_idx'].to(self.device, non_blocking=True),
                        'missing_mask': batch['missing_mask'].to(self.device, non_blocking=True),
                    }

                    # 模型推理
                    outputs = self.model(batch_device)

                    # 解析输出
                    logits = self._extract_logits(outputs)
                    if logits is None:
                        logger.error(f"Failed to extract logits from model output for batch {batch_idx}")
                        continue

                    # 转换为0-100概率值
                    preds = self._logits_to_predictions(logits)

                    # 保存第一个batch的信息用于后续可视化
                    if batch_idx == 0 and self.visualization_module.enabled:
                        first_batch_info = {
                            'batch': {k: v.clone() if torch.is_tensor(v) else v for k, v in batch.items()},
                            'predictions': preds.copy()
                        }

                    # 处理批次结果
                    batch_results = self._process_batch_results(batch, preds)

                    # 按文件分组批次结果
                    for tile_key, result in batch_results.items():
                        nc_file = Path(result['nc_file'])
                        if nc_file not in file_results_cache:
                            file_results_cache[nc_file] = {}
                        file_results_cache[nc_file][tile_key] = result

                    # 检查是否有文件的所有tiles都处理完成，如果是则立即保存
                    completed_files = self._check_and_save_completed_files(
                        file_results_cache, dataset.tile_samples
                    )
                    # 更新统计信息
                    for results in completed_files.values():
                        total_tiles_processed += len(results)
                        total_pixels_inpainted += sum(result.get('inpainted_pixels', 0) for result in results.values())

                    # 内存清理
                    if batch_idx % 200 == 0:
                        try:
                            if torch.cuda.is_available():
                                torch.cuda.synchronize()
                                torch.cuda.empty_cache()
                        except Exception:
                            pass
                        gc.collect()

                except Exception as e:
                    logger.error(f"Error processing batch {batch_idx}: {e}")
                    continue

        # 保存剩余的结果（如果有的话）
        if file_results_cache:
            for nc_file, results in file_results_cache.items():
                if results:
                    self.save_results_to_nc(results, self.inference_dir)
                    logger.info(f"Saved {len(results)} remaining tile results for {nc_file.name}")
                    total_tiles_processed += len(results)
                    total_pixels_inpainted += sum(result.get('inpainted_pixels', 0) for result in results.values())

        # 在所有NC文件保存完成后进行可视化（包含验证）
        if first_batch_info is not None and self.visualization_module.enabled:
            logger.info("Generating visualization with NC file verification...")
            num_vis_samples = self.cfg.get('inference.visualization.num_samples', 5)
            self.visualization_module.visualize_first_batch(
                first_batch_info['batch'],
                first_batch_info['predictions'],
                num_vis_samples
            )

        logger.info("Inference completed")
        # 返回总体统计信息
        return {
            'total_tiles_processed': total_tiles_processed,
            'total_pixels_inpainted': total_pixels_inpainted
        }

    def _extract_logits(self, outputs) -> Optional[torch.Tensor]:
        """从模型输出中提取logits"""
        logits = None
        if isinstance(outputs, dict) and 'inpaint' in outputs:
            inpaint_out = outputs['inpaint']
            if isinstance(inpaint_out, dict) and 'logits' in inpaint_out:
                logits = inpaint_out['logits']
            elif torch.is_tensor(inpaint_out):
                logits = inpaint_out
        elif isinstance(outputs, dict):
            # 退化：取第一个tensor
            for v in outputs.values():
                if torch.is_tensor(v):
                    logits = v
                    break
        elif torch.is_tensor(outputs):
            logits = outputs

        return logits

    def _check_and_save_completed_files(self, file_results_cache: Dict[Path, Dict[str, Any]],
                                       tile_samples: List[Dict]) -> Dict[Path, Dict[str, Any]]:
        """
        检查是否有文件的所有tiles都处理完成，如果是则立即保存并从缓存中移除

        Args:
            file_results_cache: 按文件分组的结果缓存
            tile_samples: 所有tile样本列表

        Returns:
            已完成并保存的文件结果
        """
        completed_files = {}
        files_to_remove = []

        # 统计每个文件应该有多少个tiles
        expected_tiles_per_file = {}
        for sample in tile_samples:
            nc_file = Path(sample['nc_file'])
            expected_tiles_per_file[nc_file] = expected_tiles_per_file.get(nc_file, 0) + 1

        # 检查每个文件是否完成
        for nc_file, results in file_results_cache.items():
            expected_count = expected_tiles_per_file.get(nc_file, 0)
            current_count = len(results)

            # 如果当前文件的所有tiles都处理完成
            if expected_count > 0 and current_count >= expected_count:
                # 立即保存这个文件的结果
                self.save_results_to_nc(results, self.inference_dir)
                logger.info(f"Saved {current_count} tile results for {nc_file.name} (completed)")

                # 记录已完成的文件
                completed_files[nc_file] = results
                files_to_remove.append(nc_file)

        # 从缓存中移除已完成的文件，释放内存
        for nc_file in files_to_remove:
            del file_results_cache[nc_file]

        return completed_files

    def _logits_to_predictions(self, logits: torch.Tensor) -> np.ndarray:
        """将logits转换为0-100的概率值"""
        # 转为(B,H,W)概率
        if logits.dim() == 4 and logits.size(1) >= 2:
            probs = torch.softmax(logits, dim=1)[:, 1]  # water通道
        elif logits.dim() == 4 and logits.size(1) == 1:
            probs = torch.sigmoid(logits[:, 0])
        elif logits.dim() == 3:
            probs = torch.sigmoid(logits)
        else:
            raise ValueError(f"Unsupported logits shape: {list(logits.shape)}")

        # 转换为0-100的概率值
        preds = (probs.detach().cpu().numpy() * 100).astype(np.uint8)  # (B,H,W) 0-100概率值
        return preds
    
    def _process_batch_results(self, batch: Dict[str, Any], preds: np.ndarray) -> Dict[str, Any]:
        """
        处理批次结果 - 向量化版本

        Args:
            batch: 批次数据，包含sample_info
            preds: 模型预测结果 (B, H, W) - 0-100概率值

        Returns:
            处理后的结果字典
        """
        batch_size = preds.shape[0]
        results = {}

        # 向量化提取样本信息
        if isinstance(batch['sample_info'], list):
            sample_infos = batch['sample_info']
        else:
            # 转换为列表格式以便批量处理
            sample_infos = []
            for b in range(batch_size):
                sample_info = {k: v[b] if isinstance(v, (list, torch.Tensor)) else v
                              for k, v in batch['sample_info'].items()}
                sample_infos.append(sample_info)

        # 向量化提取基本信息
        nc_files = [info['nc_file'] for info in sample_infos]
        idx_xs = np.array([int(info['idx_x']) for info in sample_infos])
        idx_ys = np.array([int(info['idx_y']) for info in sample_infos])
        center_time_idxs = np.array([int(info['center_time_idx']) for info in sample_infos])

        # 向量化处理缺失掩码
        if isinstance(batch['missing_mask'], list):
            missing_masks = np.array([mask.cpu().numpy() for mask in batch['missing_mask']])
        else:
            mmask = batch['missing_mask']
            if torch.is_tensor(mmask):
                missing_masks = mmask.cpu().numpy()
            else:
                missing_masks = np.array(mmask)

        # 向量化统计修复的像素数量 (B,)
        inpainted_pixels = np.sum(missing_masks.reshape(batch_size, -1), axis=1).astype(int)

        # 向量化批量创建结果字典
        tile_keys = [f"{nc_files[b]}_{idx_xs[b]}_{idx_ys[b]}_{center_time_idxs[b]}" for b in range(batch_size)]

        # 使用字典推导式批量创建结果
        results = {
            tile_keys[b]: {
                'nc_file': nc_files[b],
                'idx_x': idx_xs[b],
                'idx_y': idx_ys[b],
                'center_time_idx': center_time_idxs[b],
                'prediction': preds[b],  # (H, W) 0-100概率值
                'missing_mask': missing_masks[b],
                'inpainted_pixels': inpainted_pixels[b]
            }
            for b in range(batch_size)
        }

        return results

    def save_results_to_nc(self, results: Dict[str, Any], output_dir: Path):
        """保存推理结果到NetCDF文件"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 向量化按文件分组结果
        file_results: Dict[Path, List[Dict[str, Any]]] = {}

        # 提取所有文件路径并批量转换
        all_results = list(results.values())
        if not all_results:
            return

        nc_files = [result['nc_file'] for result in all_results]
        nc_paths = [Path(nc_file) if not isinstance(nc_file, Path) else nc_file for nc_file in nc_files]

        # 向量化批量分组 - 使用defaultdict提高性能
        from collections import defaultdict
        file_results_temp = defaultdict(list)

        # 批量分组
        for result, nc_path in zip(all_results, nc_paths):
            file_results_temp[nc_path].append(result)

        # 转换为普通字典
        file_results = dict(file_results_temp)

        # 保存每个文件的结果
        for nc_file, file_result_list in file_results.items():
            try:
                output_path = output_dir / f"inpainted_{nc_file.name}"

                # 若输出文件不存在，则复制原始文件
                if not output_path.exists():
                    shutil.copyfile(nc_file, output_path)

                with NcDataset(str(output_path), mode='r+') as nc:
                    if 'data' not in nc.variables:
                        raise ValueError("Variable 'data' not found in NetCDF file")
                    var = nc.variables['data']
                    dims = var.dimensions

                    def find_axis(candidates: List[str]) -> Optional[int]:
                        for i, d in enumerate(dims):
                            for c in candidates:
                                if d == c or c in d:
                                    return i
                        return None

                    time_axis = find_axis(['time'])
                    idx_x_axis = find_axis(['idx_x', 'x_idx'])
                    idx_y_axis = find_axis(['idx_y', 'y_idx'])

                    for result in file_result_list:
                        idx_x = int(result['idx_x'])
                        idx_y = int(result['idx_y'])
                        center_time_idx = int(result['center_time_idx'])
                        prediction = result['prediction']
                        missing_mask = result['missing_mask']

                        # 构造切片索引
                        indexers = [slice(None)] * len(dims)
                        if time_axis is not None:
                            indexers[time_axis] = center_time_idx
                        if idx_x_axis is not None:
                            indexers[idx_x_axis] = idx_x
                        if idx_y_axis is not None:
                            indexers[idx_y_axis] = idx_y

                        # 读取原始数据
                        original_data = var[tuple(indexers)]
                        water_mask = original_data == JRC_VALUES['water']
                        land_mask = original_data == JRC_VALUES['land']
                        no_data_mask = original_data == JRC_VALUES['no_data']
                        original_data[water_mask] = 100
                        original_data[land_mask] = 0
                        original_data[no_data_mask] = np.nan
                        # 只在缺失区域写入预测结果
                        updated_data = np.where(missing_mask, prediction, original_data)
                        var[tuple(indexers)] = updated_data

                    # 添加推理信息到文件属性
                    nc.setncattr('inpainted', 'true')
                    nc.setncattr('inpaint_timestamp', pd.Timestamp.now().isoformat())
                    nc.setncattr('inpaint_model', self.model_checkpoint)

                logger.info(f"Saved inpainted results to {output_path}")

            except Exception as e:
                logger.error(f"Error saving results for {nc_file}: {e}")





    def run_inference(self, nc_dir: str, output_dir: str,
                     region_bounds: Optional[Tuple[float, float, float, float]] = None,
                     time_range: Optional[Tuple[str, str]] = None) -> Dict[str, Any]:
        """运行推理主流程"""
        logger.info("Starting NetCDF inference process")
        start_time = time.time()

        # 查找NetCDF文件
        nc_files = find_nc_files(nc_dir, region_bounds)
        if not nc_files:
            logger.error("No NetCDF files found")
            return {}

        if time_range:
            logger.info(f"Time range filter: {time_range[0]} to {time_range[1]}")

        # 使用DataLoader方法进行推理
        all_results = self.run_inference_with_dataloader(nc_files, time_range)

        # 统计信息计算
        total_time = time.time() - start_time

        # 从推理结果中获取统计信息
        if isinstance(all_results, dict):
            tiles_processed = all_results.get('total_tiles_processed', 0)
            total_pixels = all_results.get('total_pixels_inpainted', 0)
        else:
            tiles_processed = 0
            total_pixels = 0

        inference_stats = {
            'total_tiles_processed': tiles_processed,
            'total_spatial_locations': tiles_processed,  # 向后兼容
            'total_pixels_inpainted': total_pixels,
            'total_time_seconds': total_time,
            'pixels_per_second': total_pixels / total_time if total_time > 0 else 0,
            'output_directory': output_dir
        }

        logger.info(f"Inference completed in {total_time:.2f} seconds")
        logger.info(f"Processed {len(all_results) if all_results else 0} tiles, inpainted {total_pixels} pixels")

        return inference_stats

    # def infer_and_restore(self, nc_dir: str, output_dir: str,
    #                      region_bounds: Optional[Tuple[float, float, float, float]] = None,
    #                      time_range: Optional[Tuple[str, str]] = None,
    #                      restore_regions: Optional[Dict[str, Tuple[float, float, float, float]]] = None) -> Dict[str, Any]:
    #     """推理并恢复为TIF文件的完整流程"""

    #     # 步骤1: 运行推理
    #     logger.info("=" * 50)
    #     logger.info("STEP 1: Running inference on NetCDF files")
    #     logger.info("=" * 50)

    #     inpainted_dir = Path(output_dir) / "inpainted_nc"
    #     inference_stats = self.run_inference(nc_dir, str(inpainted_dir), region_bounds, time_range)

    #     if not inference_stats:
    #         logger.error("Inference failed")
    #         return {}

    #     # 步骤2: 恢复为TIF文件
    #     logger.info("=" * 50)
    #     logger.info("STEP 2: Restoring TIF files from inpainted NetCDF")
    #     logger.info("=" * 50)

    #     tif_output_dir = Path(output_dir) / "restored_tifs"
    #     restorer = NetCDFToTIFRestorer(str(inpainted_dir), str(tif_output_dir))

    #     # 使用推理后的NetCDF文件进行恢复
    #     restoration_results = restorer.restore_all(
    #         regions=restore_regions,
    #         time_range=time_range,
    #         include_occurrence=True,
    #         num_workers=self.num_workers
    #     )

    #     # 合并结果
    #     final_results = {
    #         'inference_stats': inference_stats,
    #         'restoration_results': restoration_results,
    #         'inpainted_nc_dir': str(inpainted_dir),
    #         'restored_tif_dir': str(tif_output_dir)
    #     }

    #     logger.info("=" * 50)
    #     logger.info("PROCESS COMPLETED SUCCESSFULLY")
    #     logger.info("=" * 50)
    #     logger.info(f"Inpainted NetCDF files: {inpainted_dir}")
    #     logger.info(f"Restored TIF files: {tif_output_dir}")

    #     return final_results

def create_inference_engine(config_path: Optional[str] = None, **kwargs) -> NetCDFInferenceEngine:
    """
    创建推理引擎的便捷函数

    Args:
        config_path: 配置文件路径
        **kwargs: 配置覆盖参数

    Returns:
        NetCDFInferenceEngine对象
    """
    return NetCDFInferenceEngine(config_path, **kwargs)


def main(config_path: Optional[str] = None,
         model_checkpoint: Optional[str] = None,
         nc_dir: Optional[str] = None,
         output_dir: Optional[str] = None,
         device: Optional[str] = None,
         batch_size: Optional[int] = None,
         num_workers: Optional[int] = None,
         missing_threshold: Optional[float] = None,
         region_bounds: Optional[Tuple[float, float, float, float]] = None,
         time_range: Optional[Tuple[str, str]] = None,
         visualization_enabled: bool = True,
         num_vis_samples: int = 5):
    """
    主函数 - 直接参数接口

    Args:
        config_path: 配置文件路径
        model_checkpoint: 模型检查点路径
        nc_dir: NetCDF文件目录
        output_dir: 输出目录
        device: 推理设备
        batch_size: 批次大小
        num_workers: 工作线程数
        missing_threshold: 缺失像素比例阈值
        region_bounds: 区域边界 (min_lon, min_lat, max_lon, max_lat)
        start_time: 开始时间 (YYYY-MM-DD)
        end_time: 结束时间 (YYYY-MM-DD)
        time_range: 时间范围 (start, end) - 优先于start_time/end_time
        visualization_enabled: 是否启用可视化
        num_vis_samples: 可视化样本数量

    Returns:
        推理结果字典
    """

    # 向量化构建配置覆盖参数
    param_mapping = {
        'inference.checkpoint_path': model_checkpoint,
        'inference.nc_dir': nc_dir,
        'inference.output_dir': output_dir,
        'inference.device': device,
        'inference.batch_size': batch_size,
        'inference.num_workers': num_workers,
        'inference.missing_threshold': missing_threshold,
    }

    # 过滤非None值并构建overrides字典
    overrides = {k: v for k, v in param_mapping.items() if v is not None}

    # 添加可视化配置
    overrides.update({
        'inference.visualization.enabled': visualization_enabled,
        'inference.visualization.num_samples': num_vis_samples
    })

    # 创建推理引擎
    engine = NetCDFInferenceEngine(config_path, **overrides)

    # 从配置获取默认参数，传入参数优先
    cfg = engine.cfg
    final_nc_dir = nc_dir or cfg.get('inference.nc_dir', 'data/netcdf_files')
    final_output_dir = output_dir or cfg.get('inference.output_dir', './results')

    # 设置区域和时间范围
    final_region_bounds = region_bounds or cfg.get('inference.region_bounds')

    # 时间范围解析 - 支持两种参数方式
    final_time_range = None
    if time_range:
        # 使用 time_range 参数
        final_time_range = time_range
    else:
        # 从配置文件获取
        final_time_range = cfg.get('inference.time_range')

    print(f"Starting inference with configuration:")
    print(f"  Config file: {config_path or 'default'}")
    print(f"  NC directory: {final_nc_dir}")
    print(f"  Output directory: {final_output_dir}")
    print(f"  Device: {engine.device}")
    print(f"  Batch size: {engine.batch_size}")
    print(f"  Region bounds: {final_region_bounds}")
    print(f"  Time range: {final_time_range}")
    print(f"  Visualization enabled: {visualization_enabled}")
    if visualization_enabled:
        print(f"  Visualization samples: {num_vis_samples}")

    results = engine.run_inference(
        nc_dir=final_nc_dir,
        output_dir=final_output_dir,
        region_bounds=final_region_bounds,
        time_range=final_time_range
    )
    print(f"Inference completed. Results saved to: {final_output_dir}")
    return results

if __name__ == "__main__":
    # 示例调用 - 可以根据需要修改参数
    main(
        config_path='/home/<USER>/Water/configs/config_v20.yaml',
        nc_dir='/mnt/storage/xiaozhen/Water/Clip/JRC4',
        model_checkpoint='/mnt/storage/xiaozhen/Water/Results/checkpoints/swin_waternet_v20_2/best.pt',
        output_dir='/mnt/storage/xiaozhen/Water/Predictions/swin_waternet_v20_2',
        time_range=('2020-09', '2020-09'),
        region_bounds=(115, 28, 118, 30),
        num_workers=1,
        visualization_enabled=True,
        num_vis_samples=10
    )