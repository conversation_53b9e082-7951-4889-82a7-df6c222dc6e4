{"cells": [{"cell_type": "code", "execution_count": 10, "id": "8532f465", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:evaluation.utils:Analyzing distributions for 1503 samples\n", "INFO:evaluation.utils:Loaded bin specifications from configs/config_v20.yaml\n"]}, {"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:evaluation.utils:Histogram analysis saved to validation_samples/validation_histogram_analysis.png\n"]}, {"data": {"text/plain": ["'validation_samples/validation_histogram_analysis.png'"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "from evaluation.utils import analyze_sample_distributions\n", "import json\n", "\n", "# Load samples\n", "samples_file = \"/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/divide/validation_indices.json\"\n", "# samples_file = \"/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/train_indices.json\"\n", "# samples_file = \"/mnt/storage/xiaozhen/Water/Results/samples/swin_waternet_v20_2/val_indices.json\"\n", "with open(samples_file, 'r') as f:\n", "    data = json.load(f)\n", "\n", "if 'no_missing_samples' in data:\n", "    samples = data['no_missing_samples']\n", "else:\n", "    samples = data\n", "\n", "analyze_sample_distributions(samples, \"./validation_samples\")"]}, {"cell_type": "code", "execution_count": 9, "id": "8af02b20", "metadata": {}, "outputs": [{"data": {"text/plain": ["6490"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["len(samples)"]}, {"cell_type": "code", "execution_count": 7, "id": "c02e3720", "metadata": {}, "outputs": [{"data": {"text/plain": ["6490"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(samples)"]}, {"cell_type": "code", "execution_count": 4, "id": "ab3fa6cc", "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "string indices must be integers", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[4], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43manalyze_sample_distributions\u001b[49m\u001b[43m(\u001b[49m\u001b[43msamples\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43m./validation_samples\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/Water/evaluation/utils.py:60\u001b[0m, in \u001b[0;36manalyze_sample_distributions\u001b[0;34m(samples, output_dir, title_prefix)\u001b[0m\n\u001b[1;32m     57\u001b[0m logger\u001b[38;5;241m.\u001b[39minfo(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAnalyzing distributions for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(samples)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m samples\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     59\u001b[0m \u001b[38;5;66;03m# Extract variables\u001b[39;00m\n\u001b[0;32m---> 60\u001b[0m water_frequencies \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mmean_water_frequency\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n\u001b[1;32m     61\u001b[0m water_ratios \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mwater_proportion\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n\u001b[1;32m     62\u001b[0m years \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124myear\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n", "File \u001b[0;32m~/Water/evaluation/utils.py:60\u001b[0m, in \u001b[0;36m<listcomp>\u001b[0;34m(.0)\u001b[0m\n\u001b[1;32m     57\u001b[0m logger\u001b[38;5;241m.\u001b[39minfo(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAnalyzing distributions for \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(samples)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m samples\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m     59\u001b[0m \u001b[38;5;66;03m# Extract variables\u001b[39;00m\n\u001b[0;32m---> 60\u001b[0m water_frequencies \u001b[38;5;241m=\u001b[39m [\u001b[43ms\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mmean_water_frequency\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m]\u001b[49m \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n\u001b[1;32m     61\u001b[0m water_ratios \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mwater_proportion\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n\u001b[1;32m     62\u001b[0m years \u001b[38;5;241m=\u001b[39m [s[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124myear\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;28;01mfor\u001b[39;00m s \u001b[38;5;129;01min\u001b[39;00m samples]\n", "\u001b[0;31mTypeError\u001b[0m: string indices must be integers"]}], "source": ["analyze_sample_distributions(samples, \"./validation_samples\")"]}, {"cell_type": "code", "execution_count": 1, "id": "220c64e8", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "area_path = \"/fossfs/xiaozhen/Water/Predictions/swin_waternet_v20_2/water_area.csv/monthly_water_area.csv\"\n", "level_path = \"/fossfs/xiaozhen/Water/Predictions/swin_waternet_v20_2/water_area.csv/水位数据.xlsx\"\n", "filled_path = \"/fossfs/xiaozhen/Water/Predictions/swin_waternet_v20_2/water_area.csv/water_filled_r16.csv\"\n", "area_df = pd.read_csv(area_path)\n", "level_df = pd.read_excel(level_path)\n", "filled_df = pd.read_csv(filled_path)\n", "\n", "\n", "area_df['date'] = pd.to_datetime(area_df['month'], format='%Y-%m-%d')\n", "area_df['year'] = area_df['date'].dt.year\n", "area_df['month'] = area_df['date'].dt.month\n", "area_df['area'] = area_df['area_km2']\n", "\n", "\n", "level_df['date'] = pd.to_datetime(level_df['时间'], format='%Y-%m-%d')\n", "level_df['year'] = level_df['date'].dt.year\n", "level_df['month'] = level_df['date'].dt.month\n", "level_df = pd.DataFrame(level_df.groupby(['year', 'month'])['水位'].mean())\n", "level_df['level'] = level_df['水位']\n", "\n", "filled_df['filled'] = filled_df['area'] / 1000 / 1000\n", "filled_df = filled_df[['year', 'month', 'filled']]\n", "\n", "temp = area_df.merge(level_df, on=['year', 'month'], how='inner')\n", "df = temp.merge(filled_df, on=['year', 'month'], how='inner')"]}, {"cell_type": "code", "execution_count": 2, "id": "fc21f421", "metadata": {}, "outputs": [{"data": {"text/plain": ["SignificanceResult(statistic=np.float64(0.45847962735676334), pvalue=np.float64(1.0987927662562408e-23))"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from scipy.stats import spearmanr\n", "spearmanr(df['area'], df['level'])"]}, {"cell_type": "code", "execution_count": 3, "id": "a134e226", "metadata": {}, "outputs": [{"data": {"text/plain": ["SignificanceResult(statistic=np.float64(0.9099071997842981), pvalue=np.float64(2.8165447743736933e-165))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from scipy.stats import spearmanr\n", "spearmanr(df['filled'], df['level'])"]}, {"cell_type": "code", "execution_count": 4, "id": "55aa7336", "metadata": {}, "outputs": [], "source": ["df['abs'] = (df['filled'] - df['area']).abs()"]}, {"cell_type": "code", "execution_count": 7, "id": "69ceb7f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(1.1651743226570104)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["(df['filled'].max() - df['filled'].min()) / df['filled'].mean()"]}, {"cell_type": "code", "execution_count": 8, "id": "45f9a17c", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(0.622232836435691)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["(df['area'].max() - df['area'].min()) / df['area'].mean()"]}, {"cell_type": "code", "execution_count": 5, "id": "979672c0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>month</th>\n", "      <th>area_km2</th>\n", "      <th>date</th>\n", "      <th>year</th>\n", "      <th>area</th>\n", "      <th>水位</th>\n", "      <th>level</th>\n", "      <th>filled</th>\n", "      <th>abs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>87</th>\n", "      <td>7</td>\n", "      <td>2762.227358</td>\n", "      <td>1993-07-01</td>\n", "      <td>1993</td>\n", "      <td>2762.227358</td>\n", "      <td>19.205161</td>\n", "      <td>19.205161</td>\n", "      <td>4625.928690</td>\n", "      <td>1863.701331</td>\n", "    </tr>\n", "    <tr>\n", "      <th>147</th>\n", "      <td>7</td>\n", "      <td>2690.521811</td>\n", "      <td>1998-07-01</td>\n", "      <td>1998</td>\n", "      <td>2690.521811</td>\n", "      <td>21.401613</td>\n", "      <td>21.401613</td>\n", "      <td>4522.239581</td>\n", "      <td>1831.717770</td>\n", "    </tr>\n", "    <tr>\n", "      <th>148</th>\n", "      <td>8</td>\n", "      <td>2751.460140</td>\n", "      <td>1998-08-01</td>\n", "      <td>1998</td>\n", "      <td>2751.460140</td>\n", "      <td>21.957419</td>\n", "      <td>21.957419</td>\n", "      <td>4396.188124</td>\n", "      <td>1644.727984</td>\n", "    </tr>\n", "    <tr>\n", "      <th>72</th>\n", "      <td>4</td>\n", "      <td>2960.575492</td>\n", "      <td>1992-04-01</td>\n", "      <td>1992</td>\n", "      <td>2960.575492</td>\n", "      <td>16.216667</td>\n", "      <td>16.216667</td>\n", "      <td>4603.212094</td>\n", "      <td>1642.636602</td>\n", "    </tr>\n", "    <tr>\n", "      <th>193</th>\n", "      <td>5</td>\n", "      <td>2884.996290</td>\n", "      <td>2002-05-01</td>\n", "      <td>2002</td>\n", "      <td>2884.996290</td>\n", "      <td>17.426452</td>\n", "      <td>17.426452</td>\n", "      <td>4481.751379</td>\n", "      <td>1596.755089</td>\n", "    </tr>\n", "    <tr>\n", "      <th>422</th>\n", "      <td>6</td>\n", "      <td>2752.984654</td>\n", "      <td>2021-06-01</td>\n", "      <td>2021</td>\n", "      <td>2752.984654</td>\n", "      <td>16.899000</td>\n", "      <td>16.899000</td>\n", "      <td>4294.114747</td>\n", "      <td>1541.130092</td>\n", "    </tr>\n", "    <tr>\n", "      <th>195</th>\n", "      <td>7</td>\n", "      <td>2731.760199</td>\n", "      <td>2002-07-01</td>\n", "      <td>2002</td>\n", "      <td>2731.760199</td>\n", "      <td>18.719677</td>\n", "      <td>18.719677</td>\n", "      <td>4269.874867</td>\n", "      <td>1538.114668</td>\n", "    </tr>\n", "    <tr>\n", "      <th>39</th>\n", "      <td>7</td>\n", "      <td>2832.524252</td>\n", "      <td>1989-07-01</td>\n", "      <td>1989</td>\n", "      <td>2832.524252</td>\n", "      <td>19.175161</td>\n", "      <td>19.175161</td>\n", "      <td>4319.835814</td>\n", "      <td>1487.311563</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>9</td>\n", "      <td>2781.033659</td>\n", "      <td>1999-09-01</td>\n", "      <td>1999</td>\n", "      <td>2781.033659</td>\n", "      <td>19.361333</td>\n", "      <td>19.361333</td>\n", "      <td>4264.801739</td>\n", "      <td>1483.768080</td>\n", "    </tr>\n", "    <tr>\n", "      <th>363</th>\n", "      <td>7</td>\n", "      <td>2794.660105</td>\n", "      <td>2016-07-01</td>\n", "      <td>2016</td>\n", "      <td>2794.660105</td>\n", "      <td>20.542581</td>\n", "      <td>20.542581</td>\n", "      <td>4274.704123</td>\n", "      <td>1480.044018</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     month     area_km2       date  year         area         水位      level  \\\n", "87       7  2762.227358 1993-07-01  1993  2762.227358  19.205161  19.205161   \n", "147      7  2690.521811 1998-07-01  1998  2690.521811  21.401613  21.401613   \n", "148      8  2751.460140 1998-08-01  1998  2751.460140  21.957419  21.957419   \n", "72       4  2960.575492 1992-04-01  1992  2960.575492  16.216667  16.216667   \n", "193      5  2884.996290 2002-05-01  2002  2884.996290  17.426452  17.426452   \n", "422      6  2752.984654 2021-06-01  2021  2752.984654  16.899000  16.899000   \n", "195      7  2731.760199 2002-07-01  2002  2731.760199  18.719677  18.719677   \n", "39       7  2832.524252 1989-07-01  1989  2832.524252  19.175161  19.175161   \n", "161      9  2781.033659 1999-09-01  1999  2781.033659  19.361333  19.361333   \n", "363      7  2794.660105 2016-07-01  2016  2794.660105  20.542581  20.542581   \n", "\n", "          filled          abs  \n", "87   4625.928690  1863.701331  \n", "147  4522.239581  1831.717770  \n", "148  4396.188124  1644.727984  \n", "72   4603.212094  1642.636602  \n", "193  4481.751379  1596.755089  \n", "422  4294.114747  1541.130092  \n", "195  4269.874867  1538.114668  \n", "39   4319.835814  1487.311563  \n", "161  4264.801739  1483.768080  \n", "363  4274.704123  1480.044018  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.sort_values('abs', ascending=False).head(10)"]}, {"cell_type": "code", "execution_count": null, "id": "bf365eae", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "water", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}