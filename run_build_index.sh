#!/bin/bash
#SBATCH --ntasks=1
#SBATCH --cpus-per-task=16
#SBATCH --nodes=1
#SBATCH --account=geog_geors
#SBATCH --partition=c_foss_amd
#SBATCH --qos=normal
#SBATCH --time=2:00:00
#SBATCH --output=logs/build_index_v20_3.out
#SBATCH --mem=700G

set -e

pwd
# Initialize Conda
source /home/<USER>/miniconda/bin/activate
# Activate the geospatial environment
conda activate geospatial

# 配置参数
DATA_DIR="/fossfs/xiaozhen/Clip/JRC4"
OUTPUT_PATH="/fossfs/xiaozhen/Sample/swin_waternet_v20_3/samples.json"
CONFIG_FILE="configs/config_v20.yaml"

# 新增内存优化参数
BATCH_SIZE=3000  # 每批处理的文件数量，降低以减少内存使用
MAX_WORKERS=96  # 并行工作进程数上限
MEMORY_GB=600  # 可用内存总量（GB），留出一些余量

# 设置内存和环境变量
export OMP_NUM_THREADS=1  # 避免oversubscription
export MALLOC_TRIM_THRESHOLD_=0  # 优化内存管理
export PYTHONUNBUFFERED=1  # 实时输出
# 添加Python内存限制
export PYTHONMALLOC=malloc  # 使用标准malloc分配器
export MALLOC_MMAP_THRESHOLD_=64000  # 更积极地使用mmap

# 检查是否存在日志目录，如果不存在则创建
mkdir -p logs

# 创建输出目录
mkdir -p "$(dirname "$OUTPUT_PATH")"

# 记录开始时间
START_TIME=$(date +%s)
echo "Starting memory-optimized index building at $(date)"
echo "Data directory: $DATA_DIR"
echo "Output path: $OUTPUT_PATH"
echo "Batch size: $BATCH_SIZE"
echo "Max workers: $MAX_WORKERS (limited by $MEMORY_GB GB memory)"
echo "Max samples per file: $MAX_SAMPLES_PER_FILE"

# 显示内存情况
echo "Memory before starting:"
free -h

# 运行优化的索引构建
python data/glad/build_index_glad.py "$DATA_DIR" \
    --output "$OUTPUT_PATH" \
    --workers "$MAX_WORKERS" \
    --batch-size "$BATCH_SIZE" \
    --config "$CONFIG_FILE" \
    --memory-gb "$MEMORY_GB" \
    --spatial-sample-factor 1.0 \
    --min-samples-per-bin 10 \
    --target-samples-per-bin 100

# 计算运行时间
END_TIME=$(date +%s)
DURATION=$((END_TIME - START_TIME))
HOURS=$((DURATION / 3600))
MINUTES=$(((DURATION % 3600) / 60))
SECONDS=$((DURATION % 60))

# 显示内存情况
echo "Memory after completion:"
free -h

echo ""
echo "Index building completed!"
echo "Total time: ${HOURS}h ${MINUTES}m ${SECONDS}s"
echo "Output files:"
echo "  - Index: $OUTPUT_PATH"
echo "  - Distribution statistics: ${OUTPUT_PATH%.json}_distribution.csv"