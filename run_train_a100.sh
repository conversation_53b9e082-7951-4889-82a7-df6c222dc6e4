#!/bin/bash

# Activate the correct conda environment
source /mnt/storage/xiaozhen/miniconda3/bin/activate
conda activate geoai

# Set NumExpr threads to avoid warning (optional)
export NUMEXPR_MAX_THREADS=16

# Check GPU availability
echo "Checking GPU availability..."
nvidia-smi

# Set data paths (modify these according to your setup)
INDEX_FILE="/mnt/storage/xiaozhen/Water/Sample/Sample4/samples.json"
MISSING_DB="/mnt/storage/xiaozhen/Water/Sample/Sample4/missing_db.npz"

# Configuration file
CONFIG="configs/config_v20.yaml"

echo "Config: ${CONFIG}"
echo "Data: ${INDEX_FILE}"
echo "Missing DB: ${MISSING_DB}"

# Single GPU training only - specify which GPU to use
export CUDA_VISIBLE_DEVICES=0  # Use GPU 0, change to 1 if you want to use GPU 1
echo "Running single GPU training on GPU: $CUDA_VISIBLE_DEVICES"

python model/train_v20.py \
    --config ${CONFIG} \
    --index_file ${INDEX_FILE} \
    --missing_db ${MISSING_DB}\
    # --resume /mnt/storage/xiaozhen/Water/Results/checkpoints/last.pt

echo "Training completed!"